import {
  createSlice,
  createAsyncThunk,
  createSelector,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";
import { CarrierContact } from "state/BaseTypes";

import companiesOnboardingApi from "api/CompaniesOnboardingApi";

const MODULE_NAME = "companiesOnboarding";

type CompandiesOnboardingData = {
  details: {
    data: any;
    loading: boolean;
    creating: boolean;
    invalid: boolean;
    error: boolean;
  };
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Gets the onboarding invitation related to this token
 */
export const getOnboardingInvitation = createAsyncThunk<
  any,
  {
    invitationToken: string;
  },
  {}
>(
  `${MODULE_NAME}/getOnboardingInvitation`,
  async ({ invitationToken }, thunkAPI) => {
    const response = await companiesOnboardingApi.getOnboardingInvitation(
      invitationToken
    );

    return response.data;
  }
);

/*
 * Creates an onboarding invitation based on the fixed inviter token
 */
export const createOnboardingInvitation = createAsyncThunk<
  any,
  {
    inviterToken: string;
    identifications: any[];
    email: string;
    firstName?: string;
    lastName?: string;
  },
  {}
>(
  `${MODULE_NAME}/createOnboardingInvitation`,
  async ({ inviterToken, identifications, email, firstName, lastName }, thunkAPI) => {
    const response = await companiesOnboardingApi.createOnboardingInvitation(
      inviterToken,
      identifications,
      email,
      firstName,
      lastName
    );

    return response.data;
  }
);

/*
 * Create a new carrier on the platform
 */
export const createCarrier = createAsyncThunk<
  any,
  {
    invitationToken: string;
    carrier: any;
  },
  {}
>(
  `${MODULE_NAME}/createCarrier`,
  async ({ invitationToken, carrier }, thunkAPI) => {
    const response = await companiesOnboardingApi.createCarrier(
      invitationToken,
      carrier
    );

    return response.data;
  }
);

/*
 * Create a new user associated with this carrier on the platform
 */
export const createCarrierUser = createAsyncThunk<
  any,
  {
    invitationToken: string;
    carrierId: string;
    user: CarrierContact;
    role: string;
    password: string;
    passwordConfirmation: string;
  },
  {}
>(
  `${MODULE_NAME}/createCarrierUser`,
  async (
    { invitationToken, carrierId, user, role, password, passwordConfirmation },
    thunkAPI
  ) => {
    const response = await companiesOnboardingApi.createCarrierUser(
      invitationToken,
      carrierId,
      user,
      role,
      password,
      passwordConfirmation
    );

    return response.data;
  }
);

/*
 * Create a new help request to help this carrier with onboarding
 */
export const createHelpRequest = createAsyncThunk<
  any,
  {
    invitationToken: string;
    issue: string;
    wrongInformation?: string[];
  },
  {}
>(
  `${MODULE_NAME}/createHelpRequest`,
  async ({ invitationToken, issue, wrongInformation }, thunkAPI) => {
    const response = await companiesOnboardingApi.createHelpRequest(
      invitationToken,
      issue,
      wrongInformation
    );

    return response.data;
  }
);

const onClearError = (state: any, { payload }: PayloadAction<any>) => {
  state.details.error = false;
};

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const onboardingInvitation = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.details?.data,
    (data: any) => data
  );

export const isLoading = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.details?.loading,
    (data: any) => data
  );

export const isCreating = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.details?.creating,
    (data: any) => data
  );

export const isInvalid = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.details?.invalid,
    (data: any) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialCompaniesOnboardingState = {
  data: null,
  loading: false,
  creating: false,
  error: false,
  invalid: false,
};

const initialState: CompandiesOnboardingData = {
  details: initialCompaniesOnboardingState,
};

const companiesOnboardingSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {
    clearError: onClearError,
  },
  extraReducers: (builder) => {
    /***************************************************************************
     * ONBOARDING INVITATIONS
     **************************************************************************/

    builder.addCase(getOnboardingInvitation.fulfilled, (state, action) => {
      state.details.data = action.payload;
      state.details.loading = false;
      state.details.invalid = false;
      state.details.error = false;
    });
    builder.addCase(getOnboardingInvitation.pending, (state, action) => {
      state.details.loading = true;
      state.details.invalid = false;
      state.details.error = false;
    });
    builder.addCase(getOnboardingInvitation.rejected, (state, action) => {
      state.details.loading = false;
      state.details.invalid = true;
      state.details.error = true;
    });

    builder.addCase(createOnboardingInvitation.fulfilled, (state, action) => {
      state.details.creating = false;
      state.details.error = false;
      state.details.invalid = false;
    });
    builder.addCase(createOnboardingInvitation.pending, (state, action) => {
      state.details.creating = true;
      state.details.error = false;
      state.details.invalid = false;
    });
    builder.addCase(createOnboardingInvitation.rejected, (state, action) => {
      state.details.creating = false;
      state.details.error = true;
      state.details.invalid = true;
    });

    /***************************************************************************
     * CREATE CARRIER, USER, HELP REQUEST
     **************************************************************************/

    [createCarrier, createCarrierUser, createHelpRequest].forEach(
      (actionCreator) => {
        builder.addCase(actionCreator.fulfilled, (state, action) => {
          state.details.creating = false;
          state.details.error = false;
        });
        builder.addCase(actionCreator.pending, (state, action) => {
          state.details.creating = true;
          state.details.error = false;
        });
        builder.addCase(actionCreator.rejected, (state, action) => {
          state.details.creating = false;
          state.details.error = true;
        });
      }
    );
  },
});

export const CompaniesOnboardingState = {
  actions: {
    getOnboardingInvitation,
    createOnboardingInvitation,
    createCarrier,
    createCarrierUser,
    createHelpRequest,
    clearError: companiesOnboardingSlice.actions.clearError,
  },
  selectors: {
    onboardingInvitation,
    isLoading,
    isCreating,
    isInvalid,
  },
};

// Action creators are generated for each case reducer function
export default companiesOnboardingSlice;
