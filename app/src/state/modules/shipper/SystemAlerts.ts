import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import SystemAlertsApi, { AlertData } from "../../../api/shipper/SystemAlertsApi";
import { RootState } from "state/store";

export interface SystemAlertsState {
  alerts: AlertData[];
  loading: boolean;
  error: string | null;
}

const initialState: SystemAlertsState = {
  alerts: [],
  loading: false,
  error: null,
};

export const fetchSystemAlerts = createAsyncThunk<
  AlertData[],
  string,
  { rejectValue: string }
>("systemAlerts/fetchSystemAlerts", async (companyId, { rejectWithValue }) => {
  try {
    const response = await SystemAlertsApi.retrieveSystemAlerts(companyId);
    if (response.error) {
      return rejectWithValue(response.error);
    }
    return response.alerts || [];
  } catch (error) {
    return rejectWithValue("Failed to load system alerts");
  }
});

const systemAlertsSlice = createSlice({
  name: "systemAlerts",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchSystemAlerts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSystemAlerts.fulfilled, (state, action) => {
        state.loading = false;
        state.alerts = action.payload;
        state.error = null;
      })
      .addCase(fetchSystemAlerts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to load system alerts";
      });
  },
});

export const selectSystemAlerts = (state: RootState) => state.systemAlerts.alerts;
export const selectSystemAlertsLoading = (state: RootState) => state.systemAlerts.loading;
export const selectSystemAlertsError = (state: RootState) => state.systemAlerts.error;

export default systemAlertsSlice;
