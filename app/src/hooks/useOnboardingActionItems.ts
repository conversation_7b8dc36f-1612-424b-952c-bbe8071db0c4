import { useState, useEffect } from "react";
import { useAppSelector } from "state/hooks";
import { UsersState } from "state/modules/Users";
import CarrierSurveyApi, { SurveyStatusResponse } from "api/shipper/CarrierSurveyApi";

export const useOnboardingActionItems = () => {
  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);
  
  const [surveyStatus, setSurveyStatus] = useState<SurveyStatusResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSurveyStatus = async () => {
      if (!carrierId) return;

      try {
        setIsLoading(true);
        setError(null);
        const response = await CarrierSurveyApi.getSurveyStatus(carrierId);
        setSurveyStatus(response);
      } catch (err) {
        console.error("Error fetching survey status:", err);
        setError("Failed to load onboarding status");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSurveyStatus();
  }, [carrierId]);

  // Check if there are any pending action items
  const hasActionItems = surveyStatus 
    ? (surveyStatus.eld_onboarding_status === "pending" || 
       surveyStatus.tms_onboarding_status === "pending")
    : false;

  return {
    hasActionItems,
    isLoading,
    error,
    surveyStatus
  };
};