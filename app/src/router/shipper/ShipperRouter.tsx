import React, { lazy } from "react";
import { Redirect, Route } from "react-router-dom";

import { appRoutes, getRouteId } from "../AppRouter";

const ShipperOverviewPage = lazy(
  () => import("view/modules/shipper/overview/ShipperOverviewPage")
);
const CarriersNetworkPage = lazy(
  () => import("view/modules/shipper/carriers-network/CarriersNetworkPage")
);
const DataIntegrationsPage = lazy(
  () => import("view/modules/shipper/data-integrations/DataIntegrationsPage")
);
const ShipperCompanyPage = lazy(
  () => import("view/modules/shipper/company/ShipperCompanyPage")
);

const ShipperSystemIssuesPage = lazy(
  () => import("view/modules/shipper/system-issues/ShipperSystemIssuesPage")
);

const ShipperRouter = () => {
  return (
    <>
      <Route
        exact
        path={shipperRoutes.overview}
        component={ShipperOverviewPage}
      />

      <Route
        exact
        path={shipperRoutes.carriers}
        component={CarriersNetworkPage}
      />

      <Route
        path={shipperRoutes.dataIntegrations}
        component={DataIntegrationsPage}
      />

      <Route path={shipperRoutes.company} component={ShipperCompanyPage} />

      <Route path={shipperRoutes.systemIssues} component={ShipperSystemIssuesPage} />

      <Route
        exact
        path={appRoutes.selfServiceShipper}
        render={() => <Redirect to={shipperRoutes.overview} />}
      />
    </>
  );
};

export const shipperRoutes = {
  overview: `${appRoutes.selfServiceShipper}/overview`,
  carriers: `${appRoutes.selfServiceShipper}/carriers`,
  dataIntegrations: `${appRoutes.selfServiceShipper}/data-integrations`,
  company: `${appRoutes.selfServiceShipper}/company`,
  systemIssues: `${appRoutes.selfServiceShipper}/system-issues`,
};

export const getShipperRouteId = (history: any) =>
  getRouteId(history, shipperRoutes);

export default ShipperRouter;
