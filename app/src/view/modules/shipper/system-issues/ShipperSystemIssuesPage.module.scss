.noAlertsCircle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: #66BD7F;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.leftPanelEmptyTop {
  width: 100%;
  padding: 24px 0 8px 24px;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.leftPanelEmptyText {
  color: #666;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  margin-left: 8px; /* Add spacing between icon and text */
}

.noAlertsText {
  margin-top: 16px;
  font-size: 20px;
  color: #333;
  font-weight: 500;
}

.noAlertsSubText {
  margin-top: 8px;
  font-size: 15px;
  color: #888;
  font-weight: 400;
}


.detailsHeader .resolveIconContainer,
.detailsHeader .warningIconContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 12px;
  margin-top: 0;
}

.detailsHeader .statusIcon.resolved,
.detailsHeader .statusIcon.warning {
  margin-top: 0;
  margin-left: 0;
  margin-right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}


.container {
  display: flex;
  height: 100vh; // Full viewport height
  flex: 1;
  overflow: scroll; // Enable scrolling if content overflows
}

.leftPanel {
  width: 30%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: scroll;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex-shrink: 0; // Prevent shrinking
}

.rightPanel {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px;
  overflow: scroll; // Prevent panel overflow
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0; // Allow flex child to shrink below content size
}

.alertItem {
  padding: 16px;
  cursor: pointer;
  display: flex;
  gap: 12px;
  align-items: flex-start;
  transition: background-color 0.2s ease;
  margin: 1px 0;
  word-wrap: break-word;
  height: max-content;
  &:hover {
    background: #e6f0fc;
  }
  
  &.selected {
    border-left: 3px solid #0d65e5;
    background: #e6f0fc;
  }

  &:last-child {
    border-bottom: none;
  }
}

.dateSection {
  padding: 12px 16px;
  background: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: none;
  position: relative;
  width: 100%;
}

.dateContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #fafafa;
  padding: 1px 16px;
  border-radius: 20px;
  border: 1px solid black;
  flex-shrink: 0;
  transition: background-color 0.2s ease;
}

.dateLabel {
  background: transparent;
  padding: 0;
  border-radius: 0;
  font-weight: 600;
  font-size: 13px;
  color: grey;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
  border: none;
}

.collapseIcon {
  font-size: 12px;
  color: grey;
  transition: all 0.2s ease;
  background: transparent;
  padding: 0;
  border-radius: 0;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  height: auto;
  border: none;
}

.dateLine {
  flex: 1;
  height: 1px;
  background: grey;
  margin: 0px 2px;
  border-radius: 1px;
  color: #ff4d4f;
}

.alertContent {
  flex: 1;
  min-width: 0; // Prevents flex item from overflowing
}

.alertTitle {
  margin: 0 0 4px;
  font-weight: 600;
  font-size: 14px;
  color: grey;
  line-height: 1.4;
}

.alertSummary {
  color: grey;
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow-y: scroll;
  height: max-content
}

.detailsContainer {
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: scroll; // Prevent container overflow
  min-height: 0; // Allow flex child to shrink below content size
  
  .summaryText {
    color: #666;
    margin-bottom: 24px;
    font-size: 14px;
    line-height: 1.6;
    flex-shrink: 0;
  }
}

.errorSections {
  margin-top: 24px;
  flex: 1;
  overflow-y: scroll;
  min-height: 0; // Allow flex child to shrink below content size
  padding-right: 8px; // Space for scrollbar
  padding-bottom: 50px;
  
  // Custom scrollbar styling
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    
    &:hover {
      background: #a1a1a1;
    }
  }
}

.errorSection {
  margin-bottom: 32px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: scroll; // Allow content to be visible
  flex-shrink: 0; // Prevent shrinking
  
  &:last-child {
    margin-bottom: 24px; // Add some bottom margin for last item
  }
  padding-bottom: 20px;
}

.errorHeader {
  background: #fafafa;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  .errorTitle {
    margin: 0 !important;
    font-size: 16px;
    color: #d32f2f;
  }
}

.detailsHeader {
  display: flex;
  align-items: center;
}

.detailsHeader .statusIcon,
.detailsHeader .resolveIconContainer,
.detailsHeader .warningIconContainer {
  margin-right: 12px;
  display: flex;
  align-items: center;
}

.detailsTitle {
  margin-bottom: 0;
}

.errorContent {
  padding: 16px;
}

.errorMessage {
  margin-bottom: 20px;
}

.errorText {
  background: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 16px;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-y: scroll;
  max-height: 300px;
}

.trackingSection {
  margin-top: 16px;
}

.trackingIds {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  min-height: 44px; // Ensure minimum height for better visibility
  align-items: center;
  
  &::-webkit-scrollbar {
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    
    &:hover {
      background: #a1a1a1;
    }
  }
}

.trackingId {
  background: #e3f2fd;
  color: #1976d2;
  padding: 6px 12px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  white-space: nowrap;
  flex-shrink: 0;
  border: 1px solid #bbdefb;
}

.fallbackJson {
  background: #f8f8f8;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow: auto;
  font-size: 13px;
  line-height: 1.5;
  margin-top: 16px;
}

.statusIcon {
  font-size: 16px;
  margin-top: 2px;
  
  &.resolved {
    color: #52c41a; 
  }
  
  &.warning {
    color: #ff4d4f;
  }
}

.warningIconContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #ffebee; // Light red background
  margin-top: 2px;
  
  .statusIcon.warning {
    color: #f44336; // Darker red for the icon
    margin-top: 0; // Reset margin since container handles positioning
  }
}

.resolveIconContainer {
  display: flex;
  align-items: center;  
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #dbfae6; 
  margin-top: 2px;

  .statusIcon.resolved {
    color: #52c41a; 
    margin-top: 0; // Reset margin since container handles positioning
  }
}

.eachDay {
  height:max-content;
  display: flex;
  flex-direction: column;
}

//make style such that last element having class eachDay has a bottom-margin of 20 px
.eachDay:last-child {
  margin-bottom: 20px;
  padding-bottom: 50px; // Ensure padding for last element
}

//make .noAlerts content centered on the page
.noAlerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 200px); // Adjust height to center vertically
  text-align: center;
}