import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import SystemIssuesPage from "./ShipperSystemIssuesPage";
const mockGetCookies = require("state/modules/Users").getCookies;
const mockUseAppSelector = require("state/hooks").useAppSelector;

// Mocks
jest.mock("react-i18next", () => ({
    useTranslation: () => ({
        t: (key: string) => key,
    }),
}));

jest.mock("@fourkites/elemental-loading-indicator", () => ({
    Spinner: (props: any) => <div data-testid="spinner" {...props} />,
}));

jest.mock("state/modules/Users", () => ({
    getCookies: jest.fn(),
}));

jest.mock("state/hooks", () => ({
    useAppSelector: jest.fn(),
}));

jest.mock("@fourkites/elemental-atoms", () => ({
    AlertCircleIcon: (props: any) => <span data-testid="alert-circle-icon" {...props} />,
    ArrowDownIcon: (props: any) => <span data-testid="arrow-down-icon" {...props} />,
    ArrowRightIcon: (props: any) => <span data-testid="arrow-right-icon" {...props} />,
    CheckCircleIcon: (props: any) => <span data-testid="check-circle-icon" {...props} />,
    CheckIcon: (props: any) => <span data-testid="check-icon" {...props} />,
}));

jest.mock("./ShipperSystemIssuesPage.module.scss", () => new Proxy({}, { get: (target, prop) => prop }));


describe("SystemIssuesPage", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockGetCookies.mockReturnValue({
            authToken: "token",
            userId: "user",
            deviceId: "device",
        });
    });

    it("renders null if cookies are missing", () => {
        mockGetCookies.mockReturnValue({ authToken: null, userId: null, deviceId: null });
        mockUseAppSelector.mockReturnValue([]);
        render(<SystemIssuesPage />);
        expect(screen.queryByTestId("spinner")).not.toBeInTheDocument();
        expect(screen.queryByText("All systems normal. No issues found.")).not.toBeInTheDocument();
    });

    it("renders spinner when loading", () => {
        mockUseAppSelector
            .mockImplementationOnce(() => []) // alerts
            .mockImplementationOnce(() => true) // loading
            .mockImplementationOnce(() => null); // error
        render(<SystemIssuesPage />);
        expect(screen.getByTestId("spinner")).toBeInTheDocument();
    });

    it("renders error message when error exists", () => {
        mockUseAppSelector
            .mockImplementationOnce(() => []) // alerts
            .mockImplementationOnce(() => false) // loading
            .mockImplementationOnce(() => "Some error"); // error
        render(<SystemIssuesPage />);
        expect(screen.getByText("Some error")).toBeInTheDocument();
    });

    it("renders no alerts UI when alerts is empty", () => {
        mockUseAppSelector
            .mockImplementationOnce(() => []) // alerts
            .mockImplementationOnce(() => false) // loading
            .mockImplementationOnce(() => null); // error
        render(<SystemIssuesPage />);
        expect(screen.getByText("All systems normal. No issues found.")).toBeInTheDocument();
        expect(screen.getByText("Everything looks good!")).toBeInTheDocument();
        expect(screen.getByText("Your system is running smoothly.")).toBeInTheDocument();
        expect(screen.getByTestId("check-circle-icon")).toBeInTheDocument();
        expect(screen.getByTestId("check-icon")).toBeInTheDocument();
    });

    it("renders alerts grouped by date and selects first alert by default", () => {
        const alerts = [
            {
                uniqueId: "1",
                alertTime: [2024, 7, 1], // July 1, 2024
                resolved: false,
                title: "Alert 1",
                summary: "Summary 1",
                detailsJson: { errors: ["Error 1"], trackingIds: [["T1", "T2"]] },
            },
            {
                uniqueId: "2",
                alertTime: [2024, 7, 2], // July 2, 2024
                resolved: true,
                title: "Alert 2",
                summary: "Summary 2",
                detailsJson: {},
            },
        ];
        mockUseAppSelector.mockImplementation((selector: any) => {
            if (selector.name === "selectSystemAlerts") return alerts;
            if (selector.name === "selectSystemAlertsLoading") return false;
            if (selector.name === "selectSystemAlertsError") return null;
        });

        render(<SystemIssuesPage />);
        // Dates
        // JS months are 0-based, so July is 6
        const date1 = new Date(Date.UTC(2024, 6, 2)).toLocaleDateString();
        const date2 = new Date(Date.UTC(2024, 6, 1)).toLocaleDateString();
        expect(screen.getAllByText(date1)[0]).toBeInTheDocument();
        expect(screen.getAllByText(date2)[0]).toBeInTheDocument();
        // Alert titles
        expect(screen.getAllByText("Alert 1").length).toBeGreaterThan(0);
        expect(screen.getAllByText("Alert 2").length).toBeGreaterThan(0);
        // Selected alert details
        expect(screen.getAllByText("Summary 1").length).toBeGreaterThan(0);
        expect(screen.getAllByText("Summary 2").length).toBeGreaterThan(0);
    });

    it("shows alert details with errors and tracking IDs", () => {
        const alerts = [
            {
                uniqueId: "1",
                alertTime: [2024, 7, 1], // July 1, 2024
                resolved: false,
                title: "Alert 1",
                summary: "Summary 1",
                detailsJson: { errors: ["Error 1"], trackingIds: [["T1", "T2"]] },
            },
        ];
        mockUseAppSelector.mockImplementation((selector: any) => {
            if (selector.name === "selectSystemAlerts") return alerts;
            if (selector.name === "selectSystemAlertsLoading") return false;
            if (selector.name === "selectSystemAlertsError") return null;
        });

        render(<SystemIssuesPage />);
        expect(screen.getAllByText("Error 1").length).toBeGreaterThan(0);
        expect(screen.getByText("Tracking IDs:")).toBeInTheDocument();
        expect(screen.getByText("T1")).toBeInTheDocument();
        expect(screen.getByText("T2")).toBeInTheDocument();
    });

    it("shows fallback JSON if no errors in detailsJson", () => {
        const alerts = [
            {
                uniqueId: "2",
                alertTime: [2024, 6, 2],
                resolved: true,
                title: "Alert 2",
                summary: "Summary 2",
                detailsJson: { foo: "bar" },
            },
        ];
        mockUseAppSelector.mockImplementation((selector: any) => {
            if (selector.name === "selectSystemAlerts") return alerts;
            if (selector.name === "selectSystemAlertsLoading") return false;
            if (selector.name === "selectSystemAlertsError") return null;
        });

        render(<SystemIssuesPage />);
        expect(screen.getByText(/"foo": "bar"/)).toBeInTheDocument();
    });

    it("collapses and expands date sections", () => {
        const alerts = [
            {
                uniqueId: "1",
                alertTime: [2024, 7, 1], // July 1, 2024
                resolved: false,
                title: "Alert 1",
                summary: "Summary 1",
                detailsJson: {},
            },
        ];
        mockUseAppSelector.mockImplementation((selector: any) => {
            if (selector.name === "selectSystemAlerts") return alerts;
            if (selector.name === "selectSystemAlertsLoading") return false;
            if (selector.name === "selectSystemAlertsError") return null;
        });

        render(<SystemIssuesPage />);
        const dateLabel = screen.getByText(new Date(Date.UTC(2024, 6, 1)).toLocaleDateString());
        // Initially expanded
        expect(screen.getAllByText("Alert 1").length).toBeGreaterThan(0);
        // Collapse
        fireEvent.click(dateLabel);
        // Only the left panel alert item should be hidden, but the details panel may still show "Alert 1"
        // Find all "Alert 1" elements and check if any are inside a left panel alert item
        const leftPanelAlerts = screen.queryAllByText("Alert 1").filter(
          el => el.parentElement?.className?.includes("alertItem")
        );
        expect(leftPanelAlerts.length).toBe(0);
        // Expand
        fireEvent.click(dateLabel);
        // After expand, at least one "Alert 1" should be visible in the document
        expect(screen.getAllByText("Alert 1").length).toBeGreaterThan(0);
    });

    it("changes selected alert when alert item is clicked", () => {
        const alerts = [
            {
                uniqueId: "1",
                alertTime: [2024, 7, 1], // July 1, 2024
                resolved: false,
                title: "Alert 1",
                summary: "Summary 1",
                detailsJson: {},
            },
            {
                uniqueId: "2",
                alertTime: [2024, 7, 1], // July 1, 2024
                resolved: true,
                title: "Alert 2",
                summary: "Summary 2",
                detailsJson: {},
            },
        ];
        mockUseAppSelector.mockImplementation((selector: any) => {
            if (selector.name === "selectSystemAlerts") return alerts;
            if (selector.name === "selectSystemAlertsLoading") return false;
            if (selector.name === "selectSystemAlertsError") return null;
        });

        render(<SystemIssuesPage />);
        // Initially, first alert is selected (Alert 1)
        expect(screen.getAllByText("Alert 1").length).toBeGreaterThan(0);
        expect(screen.getAllByText("Summary 1").length).toBeGreaterThan(0);
        // Click second alert
        fireEvent.click(screen.getByText("Alert 2"));
        expect(screen.getAllByText("Alert 2").length).toBeGreaterThan(0);
        expect(screen.getAllByText("Summary 2").length).toBeGreaterThan(0);
    });
});