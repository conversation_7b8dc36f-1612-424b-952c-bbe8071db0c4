import React, { useEffect, useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { AlertData } from "../../../../api/shipper/SystemAlertsApi";
import classNames from "classnames";

import { getCookies } from "state/modules/Users";

import styles from "./ShipperSystemIssuesPage.module.scss";
import { useAppSelector } from "state/hooks";
import { AlertCircleIcon, ArrowDownIcon, ArrowRightIcon, CheckCircleIcon, CheckIcon } from "@fourkites/elemental-atoms";
import {selectSystemAlerts, selectSystemAlertsError, selectSystemAlertsLoading} from "state/modules/shipper/SystemAlerts";

type SortedAlertsByDate = [string, AlertData[]][];

const SystemIssuesPage: React.FC = () => {
  const { t } = useTranslation();

  const alerts = useAppSelector(selectSystemAlerts);
  const loading = useAppSelector(selectSystemAlertsLoading);
  const error = useAppSelector(selectSystemAlertsError);
  const { authToken, userId, deviceId } = getCookies();
  const [selectedAlert, setSelectedAlert] = useState<AlertData | null>(null);
  const [collapsedDates, setCollapsedDates] = useState<{ [key: string]: boolean }>({});

  const groupAlertsByDate = (alerts: AlertData[]): SortedAlertsByDate => {
    const grouped: { [key: string]: { alerts: AlertData[], dateObj: Date } } = {};
    
    alerts.forEach(alert => {
      const dateObj = new Date(Date.UTC(
        alert.alertTime[0],
        alert.alertTime[1] - 1,
        alert.alertTime[2]
      ));
      const dateString = dateObj.toLocaleDateString();
      
      if (!grouped[dateString]) {
        grouped[dateString] = { alerts: [], dateObj };
      }
      grouped[dateString].alerts.push(alert);
    });

    // Return sorted array of [date, alerts] tuples (descending order - newest first)
    return Object.entries(grouped)
      .sort(([, a], [, b]) => b.dateObj.getTime() - a.dateObj.getTime())
      .map(([dateString, { alerts }]) => [dateString, alerts]);
  };

  const alertsByDate = useMemo(() => 
  groupAlertsByDate(alerts), 
  [alerts]
);
  useEffect(() => {
  if (alerts.length > 0 && !selectedAlert) {
    setSelectedAlert(alertsByDate[0][1][0]);
  }
  }, [alerts]);

  const toggleDateCollapse = (date: string) => {
    setCollapsedDates(prev => ({
      ...prev,
      [date]: !prev[date]
    }));
  };

  const getStatusIcon = (alert: AlertData) => {
    if (alert.resolved) {
      return (
        <div className={styles.resolveIconContainer}>
         <CheckIcon fill="green"/>
        </div>
      );
    } else {
      // Use different icons based on alert severity or type
      return (
        <div className={styles.warningIconContainer}>
         <AlertCircleIcon fill="red"/>
        </div>
      );
    }
  };

  if (!authToken || !userId || !deviceId) {
    return null;
  }

  if (loading) {
    return <Spinner size="large" />;
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.leftPanel}></div>
        <div className={styles.rightPanel}>
          <div style={{ textAlign: 'center', marginTop: '80px', color: '#d32f2f', fontSize: '18px', fontWeight: 500 }}>
            {error}
          </div>
        </div>
      </div>
    );
  }

  if (alerts.length === 0) {
    return (
      <div className={styles.container}>
        <div className={styles.leftPanel}>
          <div className={styles.leftPanelEmptyTop}>
            <span><CheckCircleIcon /></span>
            <span className={styles.leftPanelEmptyText}>
              {t("All systems normal. No issues found.")}
            </span>
          </div>
        </div>
        <div className={styles.rightPanel}>
          <div className={styles.noAlerts}>
            <div className={styles.noAlertsCircle}>
              <CheckIcon fill="#fff" size="80" />
            </div>
            <div className={styles.noAlertsText}>
              <div>{t("Everything looks good!")}</div>
              <div className={styles.noAlertsSubText}>{t("Your system is running smoothly.")}</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
        <div className={styles.container}>
            <div className={styles.leftPanel}>
              {alertsByDate.map(([date, dateAlerts]) => (
                <div key={date} className={styles.eachDay}>
                  <div
                    className={styles.dateSection}
                    onClick={() => toggleDateCollapse(date)}
                  >
                    <div className={styles.dateContainer}>
                      <span className={styles.collapseIcon}>
                        {collapsedDates[date] ? (
                          <ArrowRightIcon />
                        ) : (
                          <ArrowDownIcon />
                        )}
                      </span>
                      <span className={styles.dateLabel}>
                        {date}
                      </span>
                    </div>
                    <div className={styles.dateLine}></div>
                  </div>
                  {!collapsedDates[date] && dateAlerts.map((alert) => (
                    <div
                      key={alert.uniqueId}
                      className={classNames(styles.alertItem, {
                        [styles.selected]: selectedAlert === alert
                      })}
                      onClick={() => setSelectedAlert(alert)}
                    >
                      {getStatusIcon(alert)}
                      <div className={styles.alertContent}>
                        <div className={styles.alertTitle}>{alert.title}</div>
                        <div className={styles.alertSummary}>{alert.summary}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          <div className={styles.rightPanel}>
            {selectedAlert && (
              <div className={styles.detailsContainer}>
                <div className={styles.detailsHeader}>
                  <span>{getStatusIcon(selectedAlert)}</span>
                  <h3 className={styles.detailsTitle} style={{ fontSize: '22px', marginBottom: '16px', color: '#333', borderBottom: '2px solid #f0f0f0', paddingBottom: '8px', fontWeight: 600 }}>
                    {selectedAlert.title}
                  </h3>
                </div>
                <div className={styles.summaryText} style={{ color: '#666', marginBottom: '24px', fontSize: '14px', lineHeight: 1.6 }}>
                  {selectedAlert.summary}
                </div>
                
                {selectedAlert.detailsJson && selectedAlert.detailsJson.errors && (
                  <div className={styles.errorSections}>
                    {selectedAlert.detailsJson.errors.map((error: string, index: number) => (
                      <div key={index} className={styles.errorSection}>
                        <div className={styles.errorHeader}>
                          <h4 className={styles.errorTitle} style={{ margin: 0, fontSize: '16px', color: '#d32f2f', fontWeight: 600 }}>
                            Error {index + 1}
                          </h4>
                        </div>
                        
                        <div className={styles.errorContent}>
                          <div className={styles.errorMessage}>
                            <span style={{ fontWeight: 600, color: '#333', fontSize: '14px', display: 'block', marginBottom: '8px' }}>{t("Error Details:")}</span>
                            <pre className={styles.errorText}>{error}</pre>
                          </div>
                          
                          {selectedAlert.detailsJson.trackingIds && 
                           selectedAlert.detailsJson.trackingIds[index] && 
                           Array.isArray(selectedAlert.detailsJson.trackingIds[index]) && 
                           selectedAlert.detailsJson.trackingIds[index].length > 0 && (
                            <div className={styles.trackingSection}>
                              <span style={{ fontWeight: 600, color: '#333', fontSize: '14px', display: 'block', marginBottom: '12px' }}>{t("Tracking IDs:")}</span>
                              <div className={styles.trackingIds}>
                                {selectedAlert.detailsJson.trackingIds[index].map((trackingId: string, trackingIndex: number) => (
                                  <span key={trackingIndex} className={styles.trackingId}>
                                    {trackingId}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                
                {!selectedAlert.detailsJson?.errors && (
                  <pre className={styles.fallbackJson}>
                    {JSON.stringify(selectedAlert.detailsJson, null, 2)}
                  </pre>
                )}
              </div>
            )}
          </div>
        </div>
  );
};

export default SystemIssuesPage;
