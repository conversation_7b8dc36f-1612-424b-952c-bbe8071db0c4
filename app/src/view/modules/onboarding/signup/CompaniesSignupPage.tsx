import React, { useState } from "react";
import { useLocation } from "react-router-dom";

import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAnalytics, useAppDispatch, useAppSelector } from "state/hooks";
import { CompaniesOnboardingState } from "state/modules/CompaniesOnboarding";
import { fourkitesUrls } from "api/http/apiUtils";
import { onNav } from "router/navigationUtils";
import { appRoutes } from "router/AppRouter";

import OnboardingFooter from "view/components/self-service/onboarding/footer/OnboardingFooter";
import OnboardingHeader from "view/components/self-service/onboarding/headers/OnboardingHeader";
import OnboardingSidePanel from "view/components/self-service/onboarding/side-panel/OnboardingSidePanel";

import { OnboardingMessage } from "../onboarding/UserFeedbackMessages";

import SignupForm from "./components/SignupForm";
import {
  InvalidInviteToken,
  SuccessMessage,
  WelcomeMessage,
} from "./components/SignupPageMessages";

import styles from "./CompaniesSignupPage.module.scss";

const CompaniesSignupPage: React.FC = () => {
  const location = useLocation();

  // Initializing pendo
  useAnalytics({ id: "" });

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();

  // Onboarding data
  const selectors = CompaniesOnboardingState.selectors;
  const invitationDetails = useAppSelector(selectors.onboardingInvitation());
  const isLoadingOnboardingInvitation = useAppSelector(selectors.isLoading());
  const isCreating = useAppSelector(selectors.isCreating());
  const isInvalidInvitationCode = useAppSelector(selectors.isInvalid());

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [form, setForm] = useState<any>({
    firstName: "",
    lastName: "",
    identificationType: "usdot", // Default to lowercase for API
    identificationNumber: "",
    email: "",
  });
  const [hasSentData, setHasSentData] = useState<boolean>(false);
  const [hasGeneralError, setHasGeneralError] = useState<boolean>(false);
  //const [helpRequested, setHelpRequested] = useState<boolean>(false);
  //const [hasSignupError, setHasSignupError] = useState<boolean>(false);

  /*****************************************************************************
   * QUERY PARAMETERS
   ****************************************************************************/

  const query = new URLSearchParams(location.search);
  const inviter = query.get("inviter");

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Helper for updating form fields
   */
  const onChangeFormField = (fieldName: string, fieldValue: string) => {
    setForm({ ...form, [fieldName]: fieldValue });
  };

  const onConfirmData = async () => {
  const createHelpRequestResponse = await dispatch(
    CompaniesOnboardingState.actions.createOnboardingInvitation({
      inviterToken: inviter || "",
      identifications: [{
        type: "usdot", // Fixed to USDOT only
        value: form?.identificationNumber
      }],
      email: form?.email,
      firstName: form?.firstName,
      lastName: form?.lastName,
    })
  );
  
  if ("error" in createHelpRequestResponse) {
    setHasGeneralError(true);
    return;
  }
  
  // Handle successful response
  const responseData = createHelpRequestResponse.payload;
  console.log(responseData);

  // Always redirect to survey page, with or without onboardingToken
  const shipperName = responseData?.shipperName || "Shipper";
  const onboardingToken = responseData?.onboardingToken;
  const companyAlreadyExists = responseData?.companyAlreadyExists || false;
  const surveyCreatedAt = responseData?.surveyCreatedAt;

  // Build URL with shipperName, onboardingToken, companyAlreadyExists, and surveyCreatedAt
  let surveyUrl = `${appRoutes.selfServiceCarrierOnboardingSurvey}?shipperName=${encodeURIComponent(shipperName)}`;

  if (onboardingToken) {
    surveyUrl += `&onboardingToken=${encodeURIComponent(onboardingToken)}`;
  }

  if (surveyCreatedAt) {
    surveyUrl += `&surveyCreatedAt=${encodeURIComponent(surveyCreatedAt)}`;
  }

  // Add companyAlreadyExists flag to URL
  surveyUrl += `&companyAlreadyExists=${companyAlreadyExists}`;

  // Navigate to survey page
  onNav(surveyUrl);
};

  /*
   * Creates a help request for this user
   */
  /*const onHelpRequested = async (
    issue: string,
    wrongInformation?: string[]
  ) => {
    const createHelpRequestResponse = await dispatch(
      CompaniesOnboardingState.actions.createHelpRequest({
        invitationToken,
        issue,
        wrongInformation,
      })
    );
    if ("error" in createHelpRequestResponse) {
      setHasGeneralError(true);
      return;
    }

    setHelpRequested(true);
  };*/

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const isLoading = isLoadingOnboardingInvitation || isCreating;

  // Invalid Invitation
  if (inviter == null || inviter === "" || isInvalidInvitationCode) {
    return <InvalidInviteToken />;
  }

  // TODO: this will be used when we handle error cases, so leaving this here for now
  // Help Requested
  //if (helpRequested) {
  //  return <HelpRequestedMessage invitationDetails={invitationDetails} />;
  //}

  // Onboarding Error
  if (hasGeneralError) {
    return (
      <OnboardingMessage
        invitationDetails={invitationDetails}
        messageType="onboarding_error"
        isSignupError={false}
      />
    );
  }

  // Onboarding Error
  if (hasSentData) {
    return <SuccessMessage {...form} />;
  }

  // Normal onboarding content
  return (
    <div className={styles.container}>
      <OnboardingSidePanel invitationDetails={invitationDetails} />

      <div id="onboarding-form">
        <OnboardingHeader />

        <WelcomeMessage />

        <div className={styles.onboardingFormContent}>
          {isLoading ? (
            <div className={styles.loader}>
              <Spinner isLoading size="medium" />
            </div>
          ) : (
            <SignupForm
              form={form}
              setForm={onChangeFormField}
              onConfirm={onConfirmData}
            />
          )}
        </div>

        <OnboardingFooter />
      </div>
    </div>
  );
};

export default CompaniesSignupPage;