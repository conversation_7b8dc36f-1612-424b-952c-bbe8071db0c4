import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { isFieldInvalid, isEmailInvalid } from "view/components/base/FormUtils";
import styles from "./SignupForm.module.scss";

const SignupForm = ({ form, setForm, onConfirm }: any) => {
  const { t } = useTranslation();
  const [confirmed, setConfirmed] = useState<boolean>(false);

  const onConfirmUserData = async () => {
    setConfirmed(true);
    const areInputsValid =
      !isFieldInvalid(form?.name) &&
      !isFieldInvalid(form?.identificationNumber) &&
      !isEmailInvalid(form?.email);

    if (areInputsValid) {
      onConfirm();
    }
  };

  return (
    <div className={styles.signupContainer}>
      <div className={styles.accountFormContainer}>
        <div className={styles.accountForm}>
          <div className={styles.accountFormRow}>
            <div className={styles.accountFormInput}>
              <Input
                label={t("Your Name")}
                errorLabel="Name is required"
                value={form?.name}
                invalid={confirmed && isFieldInvalid(form?.name)}
                onChange={(e: any) => setForm("name", e.target.value)}
                placeholder="Enter your full name"
                required
              />
            </div>
          </div>
          <div className={styles.accountFormRow}>
            <div className={styles.accountFormInput}>
              <Input
                label={t("USDOT #")}
                errorLabel="Field is required"
                value={form?.identificationNumber}
                invalid={confirmed && isFieldInvalid(form?.identificationNumber)}
                onChange={(e: any) => setForm("identificationNumber", e.target.value)}
                required
              />
            </div>
          </div>
          <div className={styles.accountFormRow}>
            <div className={styles.accountFormInput}>
              <Input
                label={`${t("Business Email")}`}
                errorLabel={t("A valid email is required")}
                value={form?.email}
                invalid={confirmed && isEmailInvalid(form?.email)}
                onChange={(e: any) => setForm("email", e.target.value)}
                required
              />
            </div>
          </div>
        </div>
      </div>
      <Button
        size="large"
        onClick={onConfirmUserData}
        data-pendo-id="signup-user-information"
      >
        {t("Confirm Data")}
      </Button>
    </div>
  );
};

export default SignupForm;