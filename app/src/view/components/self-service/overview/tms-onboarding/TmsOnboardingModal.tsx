import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { Button, PhoneIcon } from "@fourkites/elemental-atoms";
import { Modal } from "@fourkites/elemental-modal";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppSelector } from "state/hooks";
import { UsersState } from "state/modules/Users";

import { showToast } from "view/components/base/toast/Toast";
import { isFieldInvalid } from "view/components/base/FormUtils";
import PhoneInput from "view/components/self-service/carrier-onboarding-survey/PhoneInput";
import { formatPhoneNumber } from "view/components/self-service/carrier-onboarding-survey/countryData";
import VoiceOnboardingApi from "api/carrier/VoiceOnboardingApi";

import styles from "./TmsOnboardingModal.module.scss";

interface TmsOnboardingModalProps {
  show: boolean;
  onClose: () => void;
}

const TmsOnboardingModal: React.FC<TmsOnboardingModalProps> = ({
  show,
  onClose,
}) => {
  const { t } = useTranslation();

  // Get carrier permalink from current context
  const carrierPermalink = useAppSelector(UsersState.selectors.getCompanyId);

  // State for form fields
  const [phoneNumber, setPhoneNumber] = useState("");
  const [country, setCountry] = useState("US");
  const [selectedLanguage, setSelectedLanguage] = useState("en");
  const [isLoading, setIsLoading] = useState(false);
  const [callInitiated, setCallInitiated] = useState(false);

  // Language options
  const languageOptions = [
    { value: "en", label: t("English") },
    { value: "es", label: t("Spanish") },
    { value: "fr", label: t("French") },
    { value: "tr", label: t("Turkish") },
  ];

  // Validation
  const isPhoneNumberInvalid = isFieldInvalid(phoneNumber);

  const handleSubmit = async () => {
    if (isPhoneNumberInvalid) {
      showToast(t("Please enter a valid phone number"), "", "error");
      return;
    }

    if (!carrierPermalink) {
      showToast(t("Carrier information not found"), "", "error");
      return;
    }

    setIsLoading(true);

    try {
      // Format phone number with country code
      const formattedPhoneNumber = formatPhoneNumber(phoneNumber, country);

      const response = await VoiceOnboardingApi.initiateTmsOnboardingCall(
        formattedPhoneNumber,
        carrierPermalink,
        selectedLanguage
      );

      // Check if response status indicates success (2xx status codes)
      if (response?.status >= 200 && response?.status < 300) {
        setCallInitiated(true);
        showToast(
          t("TMS onboarding call initiated successfully"),
          t("You will receive a call shortly"),
          "ok"
        );
      } else {
        showToast(
          t("Failed to initiate call"),
          t("Please try again later"),
          "error"
        );
      }
    } catch (error) {
      console.error("Error initiating TMS onboarding call:", error);
      showToast(
        t("Failed to initiate call"),
        t("Please try again later"),
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setPhoneNumber("");
      setCountry("US");
      setSelectedLanguage("en");
      setCallInitiated(false);
      onClose();
    }
  };

  const handleMinimize = () => {
    // Keep the modal state but close it - user can reopen if needed
    onClose();
  };

  return (
    <Modal
      size="medium"
      title={callInitiated ? t("Call Initiated Successfully") : t("TMS Onboarding via Call")}
      show={show}
      closeButtonProps={{
        label: callInitiated ? t("Close") : t("Cancel"),
        onClick: handleClose,
        disabled: isLoading,
      }}
    >
      <div className={styles.container}>
        <div className={styles.content}>
          {!callInitiated ? (
            <>
              <div className={styles.description}>
                <p>
                  {t(
                    "We will call you to collect TMS integration details and coordinate a meeting with your TMS provider. This call will gather all necessary information for seamless integration setup."
                  )}
                </p>
              </div>

              <div className={styles.form}>
                {/* Phone Number Input */}
                <div className={styles.field}>
                  <PhoneInput
                    label={t("Phone Number")}
                    placeholder={t("Enter your phone number")}
                    errorLabel={t("Please enter a valid phone number")}
                    value={phoneNumber}
                    onChange={setPhoneNumber}
                    country={country}
                    onChangeCountry={setCountry}
                    required
                    disabled={isLoading}
                    invalid={isPhoneNumberInvalid}
                  />
                </div>

                {/* Language Selection */}
                <div className={styles.field}>
                  <label className={styles.label}>
                    {t("Preferred Language for Call")}
                    <span className={styles.required}>*</span>
                  </label>
                  <select
                    className={styles.select}
                    value={selectedLanguage}
                    onChange={(e) => setSelectedLanguage(e.target.value)}
                    disabled={isLoading}
                  >
                    {languageOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  <p className={styles.fieldDescription}>
                    {t("Select your preferred language for the call.")}
                  </p>
                </div>
              </div>

              <div className={styles.actions}>
                <Button
                  size="large"
                  theme="primary"
                  onClick={handleSubmit}
                  disabled={isLoading || isPhoneNumberInvalid}
                  data-testid="initiate-tms-call-button"
                >
                  {isLoading ? (
                    <>
                      <Spinner isLoading size="small" />
                      {t("Initiating Call...")}
                    </>
                  ) : (
                    <>
                      <PhoneIcon fill="#fff" iconClass="button-icon-left" />
                      {t("Start TMS Onboarding Call")}
                    </>
                  )}
                </Button>
              </div>
            </>
          ) : (
            <>
              {/* Post-call initiated state */}
              <div className={styles.successState}>
                <div className={styles.successIcon}>
                  <PhoneIcon fill="#10b981" className={styles.largeIcon} />
                </div>
                <div className={styles.successContent}>
                  <h3 className={styles.successTitle}>
                    {t("Call Initiated Successfully!")}
                  </h3>
                  <p className={styles.successDescription}>
                    {t(
                      "You will receive a call shortly from our team. We will collect your TMS integration details and schedule a coordination meeting with your TMS provider."
                    )}
                  </p>
                  <div className={styles.instructionsList}>
                    <p className={styles.instructionsTitle}>
                      {t("What we will collect during the call:")}
                    </p>
                    <ul className={styles.instructions}>
                      <li>{t("You'll receive a call within 2-3 minutes")}</li>
                      <li>{t("TMS POC contact details (Name, Email, Phone)")}</li>
                      <li>{t("Your TMS provider information")}</li>
                      <li>{t("3 preferred time slots for a joint call with your TMS provider")}</li>
                      <li>{t("Schedule coordination between FourKites and your TMS provider")}</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className={styles.postCallActions}>
                <Button
                  size="large"
                  theme="primary"
                  variant="outline"
                  onClick={handleMinimize}
                  data-testid="minimize-modal-button"
                >
                  {t("Minimize & Continue")}
                </Button>
                <Button
                  size="large"
                  theme="primary"
                  onClick={handleClose}
                  data-testid="close-modal-button"
                >
                  {t("Close")}
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default TmsOnboardingModal;
