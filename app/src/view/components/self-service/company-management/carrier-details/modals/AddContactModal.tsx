import { useState, useCallback } from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";
import { Modal } from "@fourkites/elemental-modal";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import {
  isEmailInvalid,
  isFieldInvalid,
  getFirstName,
  getLastName,
} from "view/components/base/FormUtils";
import UsersApi from "api/UsersApi";

import styles from "./AddContactModal.module.scss";

const AddContactModal = ({ showModal, onClose, onOk }: any) => {
  const { t } = useTranslation();

  const [contactForm, setContactForm] = useState<any>({
    name: "",
    email: "",
  });

  const [emailValidationState, setEmailValidationState] = useState<{
    isValidating: boolean;
    exists: boolean;
    error?: string;
  }>({
    isValidating: false,
    exists: false,
  });

  const validateEmailWithAPI = useCallback(async (email: string) => {
    if (!email || isEmailInvalid(email)) {
      setEmailValidationState({ isValidating: false, exists: false });
      return;
    }

    setEmailValidationState({ isValidating: true, exists: false });

    try {
      const response = await UsersApi.validateEmail(email);
      const emailExists = response?.data?.emailExists || false;
      
      setEmailValidationState({ 
        isValidating: false, 
        exists: emailExists,
        error: undefined
      });
    } catch (error) {
      setEmailValidationState({ 
        isValidating: false, 
        exists: false,
        error: 'Failed to validate email'
      });
    }
  }, []);

  const onChangeForm = (value: string, formField: string) => {
    setContactForm({
      ...contactForm,
      [formField]: value,
    });

    // Clear validation state when email changes
    if (formField === "email") {
      setEmailValidationState({
        isValidating: false,
        exists: false,
      });
    }
  };

  const onEmailBlur = () => {
    const email = contactForm?.email;
    if (email && !isEmailInvalid(email)) {
      validateEmailWithAPI(email);
    }
  };

  const onSave = async () => {
    const email = contactForm?.email;
    
    // If email is valid but hasn't been checked yet, validate it first
    if (email && !isEmailInvalid(email) && !emailValidationState.exists && !emailValidationState.isValidating) {
      await validateEmailWithAPI(email);
      return; // Let the validation complete, user can try save again after validation
    }

    // Only proceed if all validations pass
    if (
      !isEmailInvalid(contactForm?.email) &&
      !isFieldInvalid(contactForm?.name) &&
      !emailValidationState.exists &&
      !emailValidationState.isValidating
    ) {
      const firstName = getFirstName(contactForm?.name);
      const lastName = getLastName(contactForm?.name, null);

      onOk({
        email: contactForm?.email,
        firstName,
        lastName,
      });
    }
  };

  return (
    <Modal
      size="small"
      title={t("Add Carrier Contact")}
      subtitle={t("Add a new contact for this carrier")}
      show={showModal}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Send Invite"),
        onClick: onSave,
        disabled: isEmailInvalid(contactForm?.email) || 
                  isFieldInvalid(contactForm?.name) ||
                  emailValidationState.exists ||
                  emailValidationState.isValidating
      }}
    >
      <div className={styles.container}>
        <label>
          {t(
            "We will send a Signup Invite to your Carrier and get them Onboarded. " +
              "You can track this progress from the Carrier Management screen."
          )}
        </label>

        <div className={styles.inputsRow}>
          <div className={styles.inputWrapper}>
            <Input
              required
              label={`${t("Contact Name")}`}
              value={contactForm?.name}
              invalid={isFieldInvalid(contactForm?.name)}
              errorLabel={t("First name and last name are required")}
              onChange={(e: any) => onChangeForm(e.target.value, "name")}
            />
          </div>

          <div className={styles.inputWrapper}>
            <Input
              required
              label={`${t("Contact Email")}`}
              value={contactForm?.email}
              invalid={isEmailInvalid(contactForm?.email) || emailValidationState.exists}
              errorLabel={
                emailValidationState.exists ? 
                  t("Email already exists") :
                isEmailInvalid(contactForm?.email) ? 
                  t("A valid email is required") : 
                  undefined
              }
              onChange={(e: any) => onChangeForm(e.target.value, "email")}
              onBlur={onEmailBlur}
            />
            {emailValidationState.isValidating && (
              <div className={styles.emailValidationStatus}>
                <Spinner size="small" />
                <span>{t("Checking email availability...")}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default AddContactModal;
