import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import { Button, XIcon, UploadIcon } from "@fourkites/elemental-atoms";
import { Modal } from "@fourkites/elemental-modal";

import { useAppDispatch } from "state/hooks";
import { CarrierAdditionsState } from "state/modules/shipper/CarrierAdditions";

import {
  isNorthAmericaPhoneInvalid,
  isEmailInvalid,
  isFieldInvalid,
  fieldHasOnlyNumbers,
} from "view/components/base/FormUtils";
import TabbedContainer from "view/components/base/tabbed-container/TabbedContainer";
import CompanySuggestions from "view/components/self-service/company-suggestions/CompanySuggestions";

import AdditionsHandler from "../handler/AdditionsHandler";
import CarrierAdditionsFooter from "../footer/CarrierAdditionsFooter";
import CarriersOnPlatform from "./CarriersOnPlatform";
import NewCarriers from "./NewCarriers";

import styles from "./ManualCarrierAdditions.module.scss";
import ManualCarrierAdditionsProps from "./ManualCarrierAdditions.types";

const ManualCarrierAdditions = ({
  managerCompanyId,
  managedCompanyType,
  mode,
  show,
  initialAddition,
  onShowBulkCarrierAdditions,
  onClose,
}: ManualCarrierAdditionsProps) => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();

  const [selectedAdditions, setSelectedAdditions] = useState<any[]>([]);
  const [selectedInvitations, setSelectedInvitations] = useState<any[]>([]);
  const [isAddingCarriers, setIsAddingCarriers] = useState<boolean>(false);
  const [emailValidationStates, setEmailValidationStates] = useState<any>({});

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    if (initialAddition == null) {
      return;
    }

    if (typeof initialAddition === "string") {
      setSelectedInvitations([{ name: initialAddition }]);
      return;
    }

    if (initialAddition?.permalink != null) {
      // If we have a permalink,that means the company is already on FourKites
      setSelectedAdditions([initialAddition]);
    } else {
      // Otherwise, it exists only as reference and we'll need to invite them
      setSelectedInvitations([initialAddition]);
    }
  }, [initialAddition]);

  /*****************************************************************************
   * INTERNAL METHOS
   ****************************************************************************/

  // ADDITIONS

  const onSelectSuggestion = (company: any) => {
    const { permalink } = company;

    // If we don't have a permalink, its a new company
    if (!permalink) {
      // Don't allow adding repeated mcs or usdots
      if (
        selectedInvitations.filter((c: any) => {
          const selectedIdentifications = c?.identifications?.map(
            (id: any) => id?.value
          );

          return selectedIdentifications
            ?.filter((k: any) => k != null)
            .some((selectedIds: any) =>
              company?.identifications
                ?.map((id: any) => id?.value)
                .includes(selectedIds)
            );
        }).length > 0
      ) {
        return;
      }
      setSelectedInvitations([...selectedInvitations, company]);
      return;
    }

    //Otherwise, it's an existing company
    if (
      selectedAdditions.filter((c: any) => c.permalink === company.permalink)
        .length > 0
    ) {
      return;
    }
    const newAdditions = [...selectedAdditions, company];
    setSelectedAdditions([...newAdditions]);
  };

  const onUpdateSeletedAddition = (index: number, updatedSuggestion: any) => {
    let updateSuggestions = [...selectedAdditions];
    updateSuggestions[index] = updatedSuggestion;
    setSelectedAdditions(updateSuggestions);
  };

  const onRemoveSelectedAddition = (company: any) => {
    let filteredAdditions = selectedAdditions.filter(
      (c: any) => c.permalink !== company.permalink
    );
    setSelectedAdditions(filteredAdditions);
  };

  // INVITATIONS

  const onSelectInvitation = (query: string) => {
    setSelectedInvitations([...selectedInvitations, { name: query }]);
  };

  const onUpdateSeletedInvitation = (index: number, updatedInvitation: any) => {
    let updatedInvitations = [...selectedInvitations];
    updatedInvitations[index] = updatedInvitation;
    setSelectedInvitations(updatedInvitations);
  };

  const onRemoveSelectedInvitation = (index: number) => {
    let filteredInvitations = selectedInvitations.filter(
      (c: any, idx: number) => idx !== index
    );
    setSelectedInvitations(filteredInvitations);
  };

  const onEmailValidationStateChange = (validationStates: any) => {
    setEmailValidationStates(validationStates);
  };

  const onFooterConnect = () => {
    setIsAddingCarriers(true);
  };

  const onCloseManualConnection = () => {
    // Clear redux state
    dispatch(CarrierAdditionsState.actions.clearAdditions(mode));

    // Clear local state
    setIsAddingCarriers(false);
    setSelectedAdditions([]);
    setSelectedInvitations([]);

    onClose();
  };

  const anyCarrierCodeInvalid = (element: any) =>
    element?.carrier_codes ? element?.carrier_codes?.length === 0 : true;

  const anyInvitationOrAdditionInvalid = () => {
    const additionsInvalid = anyAdditionInvalid();
    const invitationsInvalid = anyInvitationInvalid();
    return additionsInvalid || invitationsInvalid;
  };

  const anyAdditionInvalid = () => {
    return selectedAdditions?.some((addition: any) => {
      return anyCarrierCodeInvalid(addition);
    });
  };

  const anyInvitationInvalid = () => {
    return selectedInvitations?.some((invitation: any, index: number) => {
      const getIdentificationByIndex = (idIndex: number) => {
        const identifications = invitation?.identifications;
        const identificationField =
          identifications?.length > 0
            ? invitation?.identifications[idIndex]?.value
            : null;

        return identificationField;
      };

      const phoneNumber =
        invitation?.contact?.phones?.length > 0
          ? invitation?.contact?.phones[0]
          : null;

      const emailValidationState = emailValidationStates[`email_${index}`];
      const emailExists = emailValidationState?.exists || false;
      const isValidatingEmail = emailValidationState?.isValidating || false;

      return (
        isFieldInvalid(invitation?.name) ||
        isFieldInvalid(invitation?.contact?.first_name) ||
        isFieldInvalid(invitation?.contact?.email) ||
        isEmailInvalid(invitation?.contact?.email) ||
        emailExists ||
        isValidatingEmail ||
        anyCarrierCodeInvalid(invitation) ||
        (!isFieldInvalid(phoneNumber) &&
          isNorthAmericaPhoneInvalid(phoneNumber)) ||
        !fieldHasOnlyNumbers(getIdentificationByIndex(0)) ||
        !fieldHasOnlyNumbers(getIdentificationByIndex(1)) ||
        (isFieldInvalid(getIdentificationByIndex(0)) &&
          isFieldInvalid(getIdentificationByIndex(1)))
      );
    });
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const tabs = [
    {
      title: `Carriers on FourKites (${selectedAdditions?.length})`,
      id: "carriersOnPlatform",
      tabComponent: (
        <CarriersOnPlatform
          data={selectedAdditions}
          onUpdateSeletedAddition={onUpdateSeletedAddition}
          onRemoveSelectedAddition={onRemoveSelectedAddition}
        />
      ),
    },
    {
      title: `New Carriers (${selectedInvitations?.length})`,
      id: "newCarriers",
      tabComponent: (
        <NewCarriers
          data={selectedInvitations}
          onUpdateSeletedInvitation={onUpdateSeletedInvitation}
          onRemoveSelectedInvitation={onRemoveSelectedInvitation}
          onEmailValidationStateChange={onEmailValidationStateChange}
        />
      ),
    },
  ];

  return (
    <Modal
      size="full"
      title={t("Connect to your FTL carriers")}
      subtitle={t("Find your full truckload carriers and get connected.")}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onCloseManualConnection,
      }}
      customHeader={
        <CustomHeader
          isAddingCarriers={isAddingCarriers}
          onCloseManualConnection={onCloseManualConnection}
          onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
        />
      }
      customFooter={
        <CarrierAdditionsFooter
          additionsNumber={selectedAdditions.length}
          invitationsNumber={selectedInvitations.length}
          disabled={anyInvitationOrAdditionInvalid()}
          isConnecting={isAddingCarriers}
          onConnect={onFooterConnect}
        />
      }
    >
      <div className={styles.container}>
        {!isAddingCarriers ? (
          <div className={styles.content}>
            <div className={styles.companySuggestionsWrapper}>
              <CompanySuggestions
                mode={mode}
                managerCompanyId={managerCompanyId}
                suggestedCompanyType={managedCompanyType}
                onSelectSuggestion={onSelectSuggestion}
                // TODO: for now, we are disabling adding not found carriers
                //onSelectNotFound={onSelectInvitation}
              />
            </div>

            <div className={styles.tabsWrapper}>
              <TabbedContainer
                tabs={tabs}
                selectedTab={"carriersOnPlatform"}
                onSelectTab={(tabId: string) => {}}
              />
            </div>
          </div>
        ) : (
          <AdditionsHandler
            mode={mode}
            managerCompanyId={managerCompanyId}
            carriersToAdd={selectedAdditions}
            carriersToInvite={selectedInvitations}
            onHandlerComplete={onCloseManualConnection}
            source={"search-add"}
          />
        )}
      </div>
    </Modal>
  );
};

const CustomHeader = ({
  isAddingCarriers,
  onCloseManualConnection,
  onShowBulkCarrierAdditions,
}: any) => {
  const { t } = useTranslation();

  return (
    <div className={styles.header}>
      <div
        data-test-id="carrier-additions-manual-title-container"
        id="title-container"
      >
        <h5>{t("Connect to your FTL carriers")}</h5>
        <span>
          {t("Find your full truckload carriers and get connected.")}
        </span>
      </div>

      <div id="right">
        <label>{t("Have your carrier list in a .csv file?")}</label>

        {!isAddingCarriers && (
          <Button
            theme="primary"
            variant="flat"
            onClick={() => {
              onShowBulkCarrierAdditions();
              onCloseManualConnection();
            }}
          >
            <span className={"button-content"}>
              <UploadIcon fill="#0e65e5" iconClass={"button-icon-left"} />
              {t("Bulk Upload")}
            </span>
          </Button>
        )}

        <button id="close" onClick={onCloseManualConnection}>
          <XIcon iconClass={styles.closeIcon} />
        </button>
      </div>
    </div>
  );
};

export default ManualCarrierAdditions;

/*
const SelectedSuggestion = ({ data, onRemoveSelectedAddition }: any) => {
  return (
    <div className={styles.selectedSuggestionContainer}>
      <CompanySummary data={data} />

      <span id="avalability-status">
        <InfoFillIcon fill="#24a148" />
        <label>Carrier Available on FourKites</label>
      </span>

      <span id="remove" onClick={() => onRemoveSelectedAddition(data)}>
        <XIcon iconClass={styles.removeIcon} />
      </span>
    </div>
  );
};

const SelectedInvitation = ({ data, onRemoveSelectedInvitation }: any) => {
  return <div className={styles.invitationsContainer}>Carrier Invitation</div>;
};*/
