import { useMemo, useState, useCallback } from "react";
import { useTranslation } from "react-i18next";

import { Table } from "@fourkites/elemental-table";
import { XIcon } from "@fourkites/elemental-atoms";

import {
  isEmailInvalid,
  isFieldInvalid,
  isNorthAmericaPhoneInvalid,
  fieldHasOnlyNumbers,
  getFirstName,
  getLastName,
} from "view/components/base/FormUtils";
import EditableCell from "view/components/base/editable-cell/EditableCell";
import MultiInput from "view/components/base/multi-input/MultiInput";
import IdentificationsIndicator from "view/components/base/company-identifications/IdentificationsIndicator";

import {
  MAX_CARRIER_CODES,
  MAX_CARRIER_CODE_LENGTH,
} from "view/components/self-service/company-management/CompanyManagementUtils";
import UsersApi from "api/UsersApi";

import styles from "./ManualCarrierAdditions.module.scss";

const NewCarriers = ({
  data,
  onUpdateSeletedInvitation,
  onRemoveSelectedInvitation,
  onEmailValidationStateChange,
}: any) => {
  const { t } = useTranslation();
  const [emailValidationState, setEmailValidationState] = useState<{[key: string]: {isValidating: boolean, exists: boolean, error?: string}}>({});

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const updateEmailValidationState = (newState: any) => {
    setEmailValidationState(newState);
    onEmailValidationStateChange?.(newState);
  };

  const validateEmailWithAPI = useCallback(async (email: string, rowIndex: number) => {
    if (!email || isEmailInvalid(email)) {
      const newState = (prev: any) => ({ ...prev, [`email_${rowIndex}`]: { isValidating: false, exists: false } });
      setEmailValidationState(newState);
      onEmailValidationStateChange?.(newState(emailValidationState));
      return;
    }

    const validatingState = (prev: any) => ({ ...prev, [`email_${rowIndex}`]: { isValidating: true, exists: false } });
    setEmailValidationState(validatingState);
    onEmailValidationStateChange?.(validatingState(emailValidationState));

    try {
      const response = await UsersApi.validateEmail(email);
      const emailExists = response?.data?.emailExists || false;
      
      const successState = (prev: any) => ({ 
        ...prev, 
        [`email_${rowIndex}`]: { 
          isValidating: false, 
          exists: emailExists,
          error: undefined
        } 
      });
      setEmailValidationState(successState);
      onEmailValidationStateChange?.(successState(emailValidationState));
    } catch (error) {
      const errorState = (prev: any) => ({ 
        ...prev, 
        [`email_${rowIndex}`]: { 
          isValidating: false, 
          exists: false,
          error: 'Failed to validate email'
        } 
      });
      setEmailValidationState(errorState);
      onEmailValidationStateChange?.(errorState(emailValidationState));
    }
  }, [emailValidationState, onEmailValidationStateChange]);

  const debounceEmailValidation = useCallback((email: string, rowIndex: number) => {
    const timeoutId = setTimeout(() => {
      validateEmailWithAPI(email, rowIndex);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [validateEmailWithAPI]);

  const getValueForField = (data: any, fieldName: string) => {
    const isValid = (value: string) => {
      return value != null && value?.trim() !== "";
    };

    const firstName = data?.contact?.first_name;
    const lastName = data?.contact?.last_name;

    const contactName =
      isValid(firstName) && isValid(lastName)
        ? `${firstName} ${lastName}`
        : isValid(firstName)
        ? firstName
        : lastName;

    const fieldValues: any = {
      carrierName: data?.name,
      contactName: contactName,
      phone: data?.contact?.phones?.length > 0 ? data?.contact?.phones[0] : "",
      email: data?.contact?.email || "",
      usdot: data?.identifications?.find((id: any) => id.type === "usdot")
        ?.value,
      mc: data?.identifications?.find((id: any) => id.type === "mc")?.value,
    };

    return fieldValues[fieldName];
  };

  const setLocalDataValueForField = (
    index: number,
    fieldName: string,
    fieldValue: string
  ) => {
    const addIdentification = (
      newAddition: any,
      fieldName: string,
      fieldValue: string
    ) => {
      const idIndex = newAddition?.identifications?.findIndex(
        (id: any) => id.type === fieldName
      );

      if (idIndex == null || idIndex === -1) {
        return [
          ...(newAddition?.identifications || []),
          { type: fieldName, value: fieldValue },
        ];
      }

      return newAddition?.identifications?.map((id: any) =>
        id.type === fieldName ? { type: fieldName, value: fieldValue } : id
      );
    };

    let addition = { ...data[index] };

    switch (fieldName) {
      case "carrierName":
        addition = {
          ...addition,
          name: fieldValue,
        };
        break;
      case "email":
        addition = {
          ...addition,
          contact: {
            ...addition.contact,
            email: fieldValue,
          },
        };
        debounceEmailValidation(fieldValue, index);
        break;
      case "phone":
        addition = {
          ...addition,
          contact: {
            ...addition.contact,
            phones: [fieldValue],
          },
        };
        break;
      case "contactName":
        addition = {
          ...addition,
          contact: {
            ...addition.contact,
            first_name: getFirstName(fieldValue),
            last_name: getLastName(fieldValue, null),
          },
        };
        break;
      case "usdot":
        addition = {
          ...addition,
          identifications: addIdentification(addition, "usdot", fieldValue),
        };
        break;
      case "mc":
        addition = {
          ...addition,
          identifications: addIdentification(addition, "mc", fieldValue),
        };
        break;
      default:
        break;
    }

    onUpdateSeletedInvitation(index, addition);
  };

  const onAddCarrierCode = (index: number, code: string) => {
    const addition = { ...data[index] };
    const updatedInvitation = {
      ...addition,
      carrier_codes: [...(addition?.carrier_codes || []), code],
    };

    onUpdateSeletedInvitation(index, updatedInvitation);
  };

  const onRemoveCarrierCode = (index: number, code: string) => {
    const addition = { ...data[index] };
    const updatedInvitation = {
      ...addition,
      carrier_codes:
        addition?.carrier_codes?.filter((c: any) => c !== code) || [],
    };

    onUpdateSeletedInvitation(index, updatedInvitation);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const multiInputValidation = {
    maxNumberOfValues: MAX_CARRIER_CODES,
    maxValueLength: MAX_CARRIER_CODE_LENGTH,
  };

  const columns = useMemo(() => {
    return [
      {
        Header: t("Carrier Name"),
        accessor: "name",
        Cell: (c: any) => {
          const rowIndex = c.row.index;
          const fieldValue = getValueForField(data[rowIndex], "carrierName");

          return (
            <EditableCell
              label={""}
              initialValue={fieldValue || ""}
              invalid={isFieldInvalid(fieldValue)}
              errorLabel={t("Carrier name is required")}
              onUpdate={(value: string) =>
                setLocalDataValueForField(rowIndex, "carrierName", value)
              }
              required={isFieldInvalid(fieldValue)}
              disabled
            />
          );
        },
      },
      {
        Header: t("USDOT#"),
        id: "usdot",
        accessor: "identifications",
        Cell: (c: any) => {
          const rowIndex = c.row.index;

          return (
            <IdentificationsIndicator
              type={"usdot"}
              label={"DOT"}
              identifications={ c?.cell?.value }
            />
          );
        },
      },
      {
        Header: t("MC#"),
        id: "mc",
        accessor: "identifications",
        Cell: (c: any) => {
          const rowIndex = c.row.index;

          return (
            <IdentificationsIndicator
              type={"mc"}
              label={"MC"}
              identifications={ c?.cell?.value }
            />
          );
        },
      },
      {
        Header: t("NMFTA SCAC"),
        id: "scac",
        accessor: "identifications",
        Cell: (c: any) => {
          const rowIndex = c.row.index;

          return (
            <IdentificationsIndicator
              type={"scac"}
              label={"NMFTA SCAC"}
              identifications={ c?.cell?.value }
            />
          );
        },
      },
      {
        Header: t("Contact Name"),
        accessor: "contactName",
        Cell: (c: any) => {
          const rowIndex = c.row.index;
          const fieldValue = getValueForField(data[rowIndex], "contactName");

          return (
            <EditableCell
              label={""}
              initialValue={fieldValue || ""}
              invalid={isFieldInvalid(fieldValue)}
              errorLabel={t("Contact name is required")}
              onUpdate={(value: string) =>
                setLocalDataValueForField(rowIndex, "contactName", value)
              }
              required={isFieldInvalid(fieldValue)}
            />
          );
        },
      },
      {
        Header: t("Contact Email"),
        accessor: "email",
        Cell: (c: any) => {
          const rowIndex = c.row.index;
          const fieldValue = getValueForField(data[rowIndex], "email");
          const validationState = emailValidationState[`email_${rowIndex}`];

          const isInvalid =
            isEmailInvalid(fieldValue) || isFieldInvalid(fieldValue);

          const getErrorLabel = () => {
            if (validationState?.error) {
              return validationState.error;
            }
            if (validationState?.exists) {
              return t("Email already exists");
            }
            if (isEmailInvalid(fieldValue)) {
              return t("Invalid email");
            }
            return t("Email is required");
          };

          const getValidationIcon = () => {
            if (validationState?.isValidating) {
              return <span style={{color: '#ffa500'}}>⏳</span>;
            }
            if (validationState?.exists) {
              return <span style={{color: '#ff4444'}}>⚠️</span>;
            }
            if (!isInvalid && fieldValue && !validationState?.isValidating) {
              return <span style={{color: '#44aa44'}}>✓</span>;
            }
            return null;
          };

          return (
            <div style={{display: 'flex', alignItems: 'center', gap: '8px'}}>
              <EditableCell
                label={""}
                initialValue={fieldValue || ""}
                invalid={isInvalid || validationState?.exists}
                errorLabel={getErrorLabel()}
                onUpdate={(value: string) =>
                  setLocalDataValueForField(rowIndex, "email", value)
                }
                required={isInvalid || validationState?.exists}
              />
              {getValidationIcon()}
            </div>
          );
        },
      },
      {
        Header: t("Contact Phone"),
        accessor: "phone",
        Cell: (c: any) => {
          const rowIndex = c.row.index;
          const fieldValue = getValueForField(data[rowIndex], "phone");

          const isInvalid =
            !isFieldInvalid(fieldValue) &&
            isNorthAmericaPhoneInvalid(fieldValue);

          return (
            <EditableCell
              label={""}
              initialValue={fieldValue || ""}
              invalid={isInvalid}
              errorLabel={
                isNorthAmericaPhoneInvalid(fieldValue)
                  ? t("Invalid phone")
                  : t("Phone is required")
              }
              onUpdate={(value: string) =>
                setLocalDataValueForField(rowIndex, "phone", value)
              }
              required={isInvalid}
            />
          );
        },
      },
      {
        Header: t("Carrier Codes"),
        accessor: "carrier_codes",
        Cell: (c: any) => {
          const rowIndex = c.row.index;
          const carrierCodesData = data[rowIndex]?.carrier_codes
            ? data[rowIndex]?.carrier_codes
            : [];
          const hasNoData = carrierCodesData?.length === 0;

          return (
            <div className={styles.carrierCodesWrapper}>
              <MultiInput
                placeholder="Enter carrier code"
                values={data[rowIndex]?.carrier_codes || []}
                defaultValues={[]}
                onAddValue={(code: string) => onAddCarrierCode(rowIndex, code)}
                onRemoveValue={(code: string) =>
                  onRemoveCarrierCode(rowIndex, code)
                }
                disabled={false}
                size="small"
                checkable
                label={""}
                required={hasNoData}
                errorLabel={t("Atleast one carrier code is required")}
                validation={multiInputValidation}
              />
            </div>
          );
        },
      },
      {
        Header: "",
        accessor: "remove",
        Cell: (c: any) => (
          <span
            id="remove"
            onClick={() => {
              onRemoveSelectedInvitation(c.row.index);
            }}
          >
            <XIcon fill="#adb5bd" iconClass={styles.removeIcon} />
          </span>
        ),
      },
    ];
  }, [
    onUpdateSeletedInvitation,
    onRemoveSelectedInvitation,
    setLocalDataValueForField,
  ]);

  const tableData = useMemo(() => {
    return data;
  }, [data]);

  const paginationParams = {
    paginated: false,
  };

  return (
    <div className={styles.tableContainer}>
      <Table
        columns={columns}
        data={tableData}
        striped
        pagination={paginationParams}
      />
    </div>
  );
};

export default NewCarriers;
