import { serviceUrls } from "api/http/apiUtils";
import { get, post } from "./http/service";
import { CarrierContact } from "state/BaseTypes";

class CompaniesOnboardingApi {
  /*****************************************************************************
   * ONBOARDING INVITATIONS
   ****************************************************************************/

  /*
   * Gets the onboarding invitation for this carrier
   */
  createOnboardingInvitation = async (
    inviterToken: string,
    identifications: any[],
    email: string,
    firstName?: string,
    lastName?: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/carriers/validate_survey_request`;
    const contact: any = { email };
    if (firstName) {
      contact.first_name = firstName;
    }
    if (lastName) {
      contact.last_name = lastName;
    }

    const response = await post(url, {
      inviterToken,
      identifications,
      contact,
    });

    return response;
  };

  /*
   * Gets the onboarding invitation for this carrier
   */
  getOnboardingInvitation = async (invitationToken: string) => {
    const url = `${serviceUrls.selfOnboarding}/carriers/onboarding-invitations?invitation-token=${invitationToken}`;
    const response = await get(url);

    return response;
  };

  /*****************************************************************************
   * CREATE CARRIER COMPANIES
   ****************************************************************************/

  /*
   * Creates a new carrier company account
   */
  createCarrier = async (invitationToken: string, carrier: any) => {
    const url = `${serviceUrls.selfOnboarding}/carriers`;
    const response = await post(url, {
      token: invitationToken,
      ...carrier,
    });

    return response;
  };

  /*****************************************************************************
   * CREATE CARRIER USERS
   ****************************************************************************/

  /*
   * Creates a new user related to this carrier
   */
  createCarrierUser = async (
    invitationToken: string,
    carrierId: string,
    user: CarrierContact,
    role: string,
    password: string,
    passwordConfirmation: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/carriers/${carrierId}/users`;
    const response = await post(url, {
      token: invitationToken,
      first_name: user.firstName,
      last_name: user.lastName,
      email: user.email,
      role,
      password,
      password_confirmation: passwordConfirmation,
      terms_agreed: true,
    });

    return response;
  };

  /*****************************************************************************
   * HELP REQUESTS
   ****************************************************************************/

  /*
   * Creates a help request on onboarding for this carrier
   */
  createHelpRequest = async (
    invitationToken: string,
    issue: string,
    wrongInformation?: string[]
  ) => {
    const url = `${serviceUrls.selfOnboarding}/carriers/help-requests`;

    const response = await post(url, {
      token: invitationToken,
      issue,
      wrong_information: wrongInformation,
    });

    return response;
  };
}

const client = new CompaniesOnboardingApi();
Object.freeze(client);
export default client;
