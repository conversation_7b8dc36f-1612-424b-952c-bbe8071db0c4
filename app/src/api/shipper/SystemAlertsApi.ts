import { get } from "../http/service";
import { serviceUrls } from "api/http/apiUtils";

export interface AlertData {
  uniqueId: number;
  alertAcknowledged: boolean;
  title: string;
  summary: string;
  alertTime: number[];
  resolved: boolean;
  detailsJson: any;
}

export interface AlertsResponse {
  alerts: AlertData[];
  error: string;
}

class SystemAlertsApi {
  retrieveSystemAlerts = async (companyId: string): Promise<AlertsResponse> => {
    const url = `${serviceUrls.selfOnboarding}/alerts/${companyId}`;
    const response = await get(url, {});
    return response.data;
  };
}

const client = new SystemAlertsApi();
Object.freeze(client);
export default client;
