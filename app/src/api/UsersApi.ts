import { serviceUrls } from "api/http/apiUtils";
import { patch, post, remove, get } from "./http/service";

const isRunningLocally = window.location.href.includes("localhost");

class UsersApi {
  /*****************************************************************************
   * USER DETAILS
   ****************************************************************************/
  /*
   * Calls API to validate token and get user details
   */
  getUserDetails = async () => {
    const url = `${serviceUrls.users}/users/validate_auth_token`;
    // Passing withCredentials only for user service apis alone (SELF-1484)
    const withCredentials = !isRunningLocally;
    
    const userResponse = await post(url, {"component": "self-service"}, {}, withCredentials);

    return userResponse;
  };

  getUserDetailsWithCompanyID = async (companyId: string) => {
    const url = `${serviceUrls.users}/users/validate_auth_token?company_id=${companyId}`;
    // Passing withCredentials only for user service apis alone (SELF-1484)
    const withCredentials = isRunningLocally;
    
    const userResponse = await post(url, {"component": "self-service"}, {}, withCredentials);

    return userResponse;
  };

  /*
   * Calls API to perform login
   */
  doUserLogin = async (username: string, password: string) => {
    const loginUrl = `${serviceUrls.users}/users/login`;
    const formData = new FormData();
    formData.append("username", username);
    formData.append("password", password);

   
    const userResponse = await post(loginUrl, formData);

    return userResponse;
  };

  /*
   * Calls API to get Archbee JWT token
   */
  getArchbeeJwt = async (authToken: string, userId: string, deviceId: string) => {
    const url = `${serviceUrls.users}/users/jwt_for_archbee`;

    const response = await get(url, {}, {
      Authorization: `Bearer ${authToken}`,
      "X-FourKitesUserId": userId,
      "X-FourKitesDeviceId": deviceId
    });

    return response.data.jwt;
  };


  /*
   * Calls API to perform logout
   */
  doUserLogout = async () => {
    const url = `${serviceUrls.users}/users/logout`;
    // Passing withCredentials only for user service apis alone (SELF-1484)
    const withCredentials = !isRunningLocally;
    
    await remove(url, {}, {}, withCredentials);
  };

  /*
   * Calls API to set terms agreed
   */
  updateTermsAgreed = async (userId: string) => {
    const url = `${serviceUrls.users}/users/${userId}`;
    // Passing withCredentials only for user service apis alone (SELF-1484)
    const withCredentials = !isRunningLocally;

    await patch(url, { termsAgreed: true }, {}, withCredentials);
  };

  /*
   * Verifies whether a password is allowed or not
   */
  // TODO: improve this when rewriting this state file
  getBlacklistedPasswords = async (password: string) => {
    const securityUrl = `${serviceUrls.users}/users/is_password_blacklisted`;
    const formData = new FormData();
    formData.append("password", password);
    const response = await post(securityUrl, formData);
    return response;
  };

  /*****************************************************************************
   * EMAIL VALIDATION
   ****************************************************************************/
  /*
   * Calls API to validate if email exists and get user details
   */
  validateEmail = async (email: string) => {
    const url = `${serviceUrls.selfOnboarding}/users/validate-email?email=${encodeURIComponent(email)}`;
    
    const response = await get(url);
    return response;
  };

  /*****************************************************************************
   * RESET PASSWORD
   ****************************************************************************/
  /*
   * Calls API to reset the password
   */
  doPasswordReset = async (emailId: string) => {
    const url = `${serviceUrls.users}/users/forgot_password`;
    const response = await post(url, { emailAddress: emailId });

    return response;
  };
}

const client = new UsersApi();
Object.freeze(client);
export default client;
